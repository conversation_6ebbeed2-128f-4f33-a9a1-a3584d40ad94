package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.group.GroupType;
import cn.hanyi.ctm.dto.group.EventGroupQueryBuilderDto;
import cn.hanyi.ctm.dto.group.EventGroupQueryItemsPropertyDto;
import cn.hanyi.ctm.dto.group.EventGroupQueryPropertyDto;
import cn.hanyi.ctm.entity.EventGroup;
import cn.hanyi.ctm.entity.EventGroupDto;
import cn.hanyi.ctm.entity.EventGroupQuery;
import cn.hanyi.ctm.properites.EventResultQueryProperties;
import cn.hanyi.ctm.repository.EventGroupQueryRepository;
import cn.hanyi.ctm.repository.EventGroupRepository;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class EventGroupService  extends BaseService<EventGroup, EventGroupDto, EventGroupRepository> {

    @Autowired
    private EventResultQueryProperties eventResultQueryProperties;

    @Autowired
    private EventGroupQueryRepository eventGroupQueryRepository;

    @Autowired
    private EventGroupRepository eventGroupRepository;

    @Override
    public void afterMapToDto(EventGroup entity, EventGroupDto dto) {
        super.afterMapToDto(entity, dto);
        dto.setGroups(queryGroupsByGroupId(entity.getId()));

    }

    public List<EventGroupQueryBuilderDto> getBuilder() {
        Function<String, List<EventGroupQueryBuilderDto.QueryBuilderItemDto>> getItems = t ->
                eventResultQueryProperties.getQueryType().getOrDefault(t, List.of())
                        .stream()
                        .map(j -> new EventGroupQueryBuilderDto.QueryBuilderItemDto(
                                j.getQueryTypeLabel(),
                                j.getQueryType(),
                                j.getQueryValueType()
                        ))
                        .collect(Collectors.toList());

        List<EventGroupQueryBuilderDto> builder = eventResultQueryProperties.getQuery()
                .stream().filter(i -> !i.getTemplateId().equals(-1)).map(i -> {
                    EventGroupQueryBuilderDto dto = new EventGroupQueryBuilderDto();
                    dto.setPropertyLabel(i.getPropertyLabel());
                    dto.setPropertyName(i.getPropertyName());
                    dto.setTemplateId(i.getTemplateId());
                    dto.setPropertySource(i.getPropertySource());
                    dto.setPropertyColumn(i.getPropertyColumn());
                    dto.setPropertyType(i.getPropertyType());
                    dto.setInputType(i.getInputType());
                    dto.setQueryItems(getItems.apply(i.getQueryItemType()));
                    return dto;
                })
                .collect(Collectors.toList());
        return builder;
    }

    public EventGroupQueryItemsPropertyDto batchNewQuery(long groupId, EventGroupQueryItemsPropertyDto dto) {
        EventGroup group = require(groupId);
        eventGroupQueryRepository.deleteByGroupId(groupId);
        List<EventGroupQuery> queries = new ArrayList<>();
        group.setLogic(dto.getLogic());

        int groupIndex = 0;
        for (EventGroupQueryPropertyDto g : dto.getGroups()) {
            int finalGroupIndex = groupIndex;
            g.getItems().forEach(i -> {
                EventGroupQuery eventGroupQuery = new EventGroupQuery();
                eventGroupQuery.setProperty(i);
                eventGroupQuery.setGroup(group);
                eventGroupQuery.setTag(g.getTag());
                eventGroupQuery.setSequence(finalGroupIndex); // 获取后自增
                queries.add(eventGroupQuery);
            });
            groupIndex++;
        }

        eventGroupQueryRepository.saveAll(queries);
        return dto;
    }


    public List<EventGroup> findByEventId(Long eventId) {
        // 直接获取企业下所有的分组来查询对应的事件
        // 只查询手动分组

        return Optional.ofNullable(
                        scopeQuery(
                                EntityScopeStrategyType.ORGANIZATION,
                                () -> repository.findAll((r, q, cb) -> cb.equal(r.get("type"), GroupType.MANUAL))
                        )
                )
                .stream()
                .flatMap(List::stream)
                .filter(g -> findQueryByGroupId(g.getId()).stream().anyMatch(q -> eventId.toString().equals(q.getProperty().getQueryValue()) && q.getProperty().getTemplateId().equals(-1)))
                .collect(Collectors.toList());
    }

    public List<EventGroupQueryPropertyDto> queryGroupsByGroupId(Long groupId) {
        return findQueryByGroupId(groupId).stream().collect(Collectors.groupingBy(EventGroupQuery::getSequence))
                .values().stream().map(eventGroupQueries -> {
                    EventGroupQueryPropertyDto itemsDto = new EventGroupQueryPropertyDto();
                    itemsDto.setTag(eventGroupQueries.get(0).getTag());
                    itemsDto.setLogic(eventGroupQueries.get(0).getLogic());
                    itemsDto.setItems(eventGroupQueries.stream().map(EventGroupQuery::getProperty).collect(Collectors.toList()));
                    return itemsDto;
                }).collect(Collectors.toList());
    }

    public List<EventGroupQuery> findQueryByGroupId(Long groupId) {
        return eventGroupQueryRepository.findByGroupId(groupId);
    }

}
